{"__meta": {"id": "01K9Z4CV588PB3G9JVDJ6D6RDD", "datetime": "2025-11-13 18:32:15", "utime": **********.912345, "method": "GET", "uri": "/staff/students?tableFilters[student_filter][school_session_id]=4&tableFilters[student_filter][semester_id]=2&tableFilters[student_filter][level_id]=3&tableFilters[student_filter][programme_id]=1", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[18:32:15] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.611795, "xdebug_link": null, "collector": "log"}, {"message": "[18:32:15] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.612342, "xdebug_link": null, "collector": "log"}]}, "time": {"start": *********4.987344, "end": **********.912363, "duration": 0.9250190258026123, "duration_str": "925ms", "measures": [{"label": "Booting", "start": *********4.987344, "relative_start": 0, "end": **********.300834, "relative_end": **********.300834, "duration": 0.****************, "duration_str": "313ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.300853, "relative_start": 0.****************, "end": **********.912365, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "612ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.311082, "relative_start": 0.*****************, "end": **********.312096, "relative_end": **********.312096, "duration": 0.0010142326354980469, "duration_str": "1.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.475504, "relative_start": 0.****************, "end": **********.475504, "relative_end": **********.475504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.49223, "relative_start": 0.****************, "end": **********.49223, "relative_end": **********.49223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.503917, "relative_start": 0.5165729522705078, "end": **********.503917, "relative_end": **********.503917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.517302, "relative_start": 0.5299580097198486, "end": **********.517302, "relative_end": **********.517302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.527875, "relative_start": 0.5405309200286865, "end": **********.527875, "relative_end": **********.527875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.534741, "relative_start": 0.5473968982696533, "end": **********.534741, "relative_end": **********.534741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.54669, "relative_start": 0.5593459606170654, "end": **********.54669, "relative_end": **********.54669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.55567, "relative_start": 0.5683259963989258, "end": **********.55567, "relative_end": **********.55567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.560368, "relative_start": 0.5730240345001221, "end": **********.560368, "relative_end": **********.560368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.57911, "relative_start": 0.5917658805847168, "end": **********.57911, "relative_end": **********.57911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.579698, "relative_start": 0.5923540592193604, "end": **********.579698, "relative_end": **********.579698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.627914, "relative_start": 0.6405699253082275, "end": **********.627914, "relative_end": **********.627914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.630435, "relative_start": 0.6430909633636475, "end": **********.630435, "relative_end": **********.630435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.646836, "relative_start": 0.659492015838623, "end": **********.646836, "relative_end": **********.646836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.school-details", "start": **********.719252, "relative_start": 0.7319080829620361, "end": **********.719252, "relative_end": **********.719252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.761091, "relative_start": 0.7737469673156738, "end": **********.761091, "relative_end": **********.761091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.welcome-user", "start": **********.763999, "relative_start": 0.7766549587249756, "end": **********.763999, "relative_end": **********.763999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd1940d56c351ec3a267a57ee9c54a44", "start": **********.775557, "relative_start": 0.7882130146026611, "end": **********.775557, "relative_end": **********.775557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-knowledge-base::livewire.help-menu", "start": **********.778883, "relative_start": 0.7915389537811279, "end": **********.778883, "relative_end": **********.778883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-easy-footer::easy-footer", "start": **********.783272, "relative_start": 0.7959280014038086, "end": **********.783272, "relative_end": **********.783272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-easy-footer::github-version", "start": **********.789106, "relative_start": 0.8017618656158447, "end": **********.789106, "relative_end": **********.789106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.analytics-tag", "start": **********.890218, "relative_start": 0.9028739929199219, "end": **********.890218, "relative_end": **********.890218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f0ee69f5395275435701a1ff333f6962", "start": **********.896549, "relative_start": 0.9092049598693848, "end": **********.896549, "relative_end": **********.896549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-knowledge-base::livewire.modals", "start": **********.898846, "relative_start": 0.9115018844604492, "end": **********.898846, "relative_end": **********.898846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.901752, "relative_start": 0.9144079685211182, "end": **********.902094, "relative_end": **********.902094, "duration": 0.0003418922424316406, "duration_str": "342μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.911544, "relative_start": 0.9242000579833984, "end": **********.911631, "relative_end": **********.911631, "duration": 8.702278137207031e-05, "duration_str": "87μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8357728, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 24, "nb_templates": 24, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.475439, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.492164, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.503854, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.517227, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.527821, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.534646, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.546636, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.555553, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.560304, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.579012, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.579651, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.627842, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.630382, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.64678, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament.hooks.school-details", "param_count": null, "params": [], "start": **********.719186, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.phpfilament.hooks.school-details", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fschool-details.blade.php&line=1", "ajax": false, "filename": "school-details.blade.php", "line": "?"}}, {"name": "__components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.761022, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}}, {"name": "filament.hooks.welcome-user", "param_count": null, "params": [], "start": **********.763944, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/welcome-user.blade.phpfilament.hooks.welcome-user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fwelcome-user.blade.php&line=1", "ajax": false, "filename": "welcome-user.blade.php", "line": "?"}}, {"name": "__components::fd1940d56c351ec3a267a57ee9c54a44", "param_count": null, "params": [], "start": **********.775496, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/fd1940d56c351ec3a267a57ee9c54a44.blade.php__components::fd1940d56c351ec3a267a57ee9c54a44", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Ffd1940d56c351ec3a267a57ee9c54a44.blade.php&line=1", "ajax": false, "filename": "fd1940d56c351ec3a267a57ee9c54a44.blade.php", "line": "?"}}, {"name": "filament-knowledge-base::livewire.help-menu", "param_count": null, "params": [], "start": **********.778813, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\guava\\filament-knowledge-base\\src\\/../resources/views/livewire/help-menu.blade.phpfilament-knowledge-base::livewire.help-menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fguava%2Ffilament-knowledge-base%2Fresources%2Fviews%2Flivewire%2Fhelp-menu.blade.php&line=1", "ajax": false, "filename": "help-menu.blade.php", "line": "?"}}, {"name": "filament-easy-footer::easy-footer", "param_count": null, "params": [], "start": **********.783211, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\/../resources/views/easy-footer.blade.phpfilament-easy-footer::easy-footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fdevonab%2Ffilament-easy-footer%2Fresources%2Fviews%2Feasy-footer.blade.php&line=1", "ajax": false, "filename": "easy-footer.blade.php", "line": "?"}}, {"name": "filament-easy-footer::github-version", "param_count": null, "params": [], "start": **********.789062, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\/../resources/views/github-version.blade.phpfilament-easy-footer::github-version", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fdevonab%2Ffilament-easy-footer%2Fresources%2Fviews%2Fgithub-version.blade.php&line=1", "ajax": false, "filename": "github-version.blade.php", "line": "?"}}, {"name": "filament.hooks.analytics-tag", "param_count": null, "params": [], "start": **********.89016, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/analytics-tag.blade.phpfilament.hooks.analytics-tag", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fanalytics-tag.blade.php&line=1", "ajax": false, "filename": "analytics-tag.blade.php", "line": "?"}}, {"name": "__components::f0ee69f5395275435701a1ff333f6962", "param_count": null, "params": [], "start": **********.896498, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/f0ee69f5395275435701a1ff333f6962.blade.php__components::f0ee69f5395275435701a1ff333f6962", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Ff0ee69f5395275435701a1ff333f6962.blade.php&line=1", "ajax": false, "filename": "f0ee69f5395275435701a1ff333f6962.blade.php", "line": "?"}}, {"name": "filament-knowledge-base::livewire.modals", "param_count": null, "params": [], "start": **********.898777, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\guava\\filament-knowledge-base\\src\\/../resources/views/livewire/modals.blade.phpfilament-knowledge-base::livewire.modals", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fguava%2Ffilament-knowledge-base%2Fresources%2Fviews%2Flivewire%2Fmodals.blade.php&line=1", "ajax": false, "filename": "modals.blade.php", "line": "?"}}]}, "queries": {"count": 57, "nb_statements": 56, "nb_visible_statements": 57, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0712, "accumulated_duration_str": "71.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.316881, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'EEuMbIiZ26t6M7o3bQNpIUXpJDiaeiXoW0yIoOgx' limit 1", "type": "query", "params": [], "bindings": ["EEuMbIiZ26t6M7o3bQNpIUXpJDiaeiXoW0yIoOgx"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.317558, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 8.427}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.3271759, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 8.427, "width_percent": 0.857}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.346603, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 9.284, "width_percent": 0.73}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.3490489, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 10.014, "width_percent": 0.59}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-11-13' and date(`semester_end`) >= '2025-11-13' limit 1", "type": "query", "params": [], "bindings": [3, "2025-11-13", "2025-11-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.353034, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 10.604, "width_percent": 1.362}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id`) and `users`.`role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and not exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `is_graduated` = 1) and not exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `is_withdrawn` = 1) and exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '4' and `semester_id` = '2') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, 1, 1, "4", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 92}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.365803, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "ListStudents.php:85", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource%2FPages%2FListStudents.php&line=85", "ajax": false, "filename": "ListStudents.php", "line": "85"}, "connection": "racoed", "explain": null, "start_percent": 11.966, "width_percent": 6.615}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id`) and `users`.`role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 0) and exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '4' and `semester_id` = '2') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 0, "4", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 97}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.37413, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ListStudents.php:85", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource%2FPages%2FListStudents.php&line=85", "ajax": false, "filename": "ListStudents.php", "line": "85"}, "connection": "racoed", "explain": null, "start_percent": 18.581, "width_percent": 1.011}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id`) and `users`.`role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 2) and exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '4' and `semester_id` = '2') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 2, "4", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.377716, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ListStudents.php:85", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource%2FPages%2FListStudents.php&line=85", "ajax": false, "filename": "ListStudents.php", "line": "85"}, "connection": "racoed", "explain": null, "start_percent": 19.593, "width_percent": 0.927}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id`) and `users`.`role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `is_graduated` = 1) and exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '4' and `semester_id` = '2') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, 1, "4", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 107}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.380888, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ListStudents.php:85", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource%2FPages%2FListStudents.php&line=85", "ajax": false, "filename": "ListStudents.php", "line": "85"}, "connection": "racoed", "explain": null, "start_percent": 20.52, "width_percent": 1.152}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id`) and `users`.`role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `is_withdrawn` = 1) and exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '4' and `semester_id` = '2') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, 1, "4", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.384395, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "ListStudents.php:85", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource/Pages/ListStudents.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource%2FPages%2FListStudents.php&line=85", "ajax": false, "filename": "ListStudents.php", "line": "85"}, "connection": "racoed", "explain": null, "start_percent": 21.671, "width_percent": 2.711}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '4' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 277}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.40159, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "StudentResource.php:277", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=277", "ajax": false, "filename": "StudentResource.php", "line": "277"}, "connection": "racoed", "explain": null, "start_percent": 24.382, "width_percent": 3.862}, {"sql": "select * from `semesters` where `semesters`.`id` = '2' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 283}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.40894, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:283", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 283}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=283", "ajax": false, "filename": "StudentResource.php", "line": "283"}, "connection": "racoed", "explain": null, "start_percent": 28.244, "width_percent": 0.702}, {"sql": "select * from `levels` where `levels`.`id` = '3' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 289}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.411862, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:289", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 289}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=289", "ajax": false, "filename": "StudentResource.php", "line": "289"}, "connection": "racoed", "explain": null, "start_percent": 28.947, "width_percent": 0.772}, {"sql": "select * from `programmes` where `programmes`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 295}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.414201, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:295", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 295}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=295", "ajax": false, "filename": "StudentResource.php", "line": "295"}, "connection": "racoed", "explain": null, "start_percent": 29.719, "width_percent": 0.702}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 221}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.433131, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:221", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 221}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=221", "ajax": false, "filename": "StudentResource.php", "line": "221"}, "connection": "racoed", "explain": null, "start_percent": 30.421, "width_percent": 0.857}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 232}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.484308, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:232", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=232", "ajax": false, "filename": "StudentResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 31.278, "width_percent": 0.941}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 242}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.497226, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:242", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=242", "ajax": false, "filename": "StudentResource.php", "line": "242"}, "connection": "racoed", "explain": null, "start_percent": 32.219, "width_percent": 0.857}, {"sql": "select `name`, `id` from `programmes` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 254}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.511791, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:254", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 254}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=254", "ajax": false, "filename": "StudentResource.php", "line": "254"}, "connection": "racoed", "explain": null, "start_percent": 33.076, "width_percent": 0.843}, {"sql": "select count(*) as aggregate from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.6503222, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "AssessmentResource.php:55", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FAssessmentResource.php&line=55", "ajax": false, "filename": "AssessmentResource.php", "line": "55"}, "connection": "racoed", "explain": null, "start_percent": 33.919, "width_percent": 5.576}, {"sql": "select count(*) as aggregate from `courses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.657919, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "CourseResource.php:56", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FCourseResource.php&line=56", "ajax": false, "filename": "CourseResource.php", "line": "56"}, "connection": "racoed", "explain": null, "start_percent": 39.494, "width_percent": 2.528}, {"sql": "select count(*) as aggregate from `departments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.6631029, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DepartmentResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FDepartmentResource.php&line=51", "ajax": false, "filename": "DepartmentResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 42.022, "width_percent": 0.843}, {"sql": "select count(*) as aggregate from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.666471, "duration": 0.00994, "duration_str": "9.94ms", "memory": 0, "memory_str": null, "filename": "GradeResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FGradeResource.php&line=51", "ajax": false, "filename": "GradeResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 42.865, "width_percent": 13.961}, {"sql": "select count(*) as aggregate from `invoices` where `invoice_status` = 3 and ((`invoices`.`payable_type` = 'App\\\\Models\\\\Fee' and exists (select * from `fees` where `invoices`.`payable_id` = `fees`.`id`)) or (`invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and exists (select * from `registrations` where `invoices`.`payable_id` = `registrations`.`id`))) and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3, "App\\Models\\Fee", "App\\Models\\Registration"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.680962, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "InvoiceResource.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FInvoiceResource.php&line=53", "ajax": false, "filename": "InvoiceResource.php", "line": "53"}, "connection": "racoed", "explain": null, "start_percent": 56.826, "width_percent": 2.346}, {"sql": "select count(*) as aggregate from `levels` where `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.686621, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "LevelResource.php:50", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FLevelResource.php&line=50", "ajax": false, "filename": "LevelResource.php", "line": "50"}, "connection": "racoed", "explain": null, "start_percent": 59.171, "width_percent": 1.053}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.690572, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:62", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FNewsResource.php&line=62", "ajax": false, "filename": "NewsResource.php", "line": "62"}, "connection": "racoed", "explain": null, "start_percent": 60.225, "width_percent": 1.208}, {"sql": "select count(*) as aggregate from `programmes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.6944902, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ProgrammeResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FProgrammeResource.php&line=54", "ajax": false, "filename": "ProgrammeResource.php", "line": "54"}, "connection": "racoed", "explain": null, "start_percent": 61.433, "width_percent": 0.646}, {"sql": "select count(*) as aggregate from `schools`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.6979802, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "SchoolResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolResource.php&line=49", "ajax": false, "filename": "SchoolResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 62.079, "width_percent": 3.146}, {"sql": "select count(*) as aggregate from `school_sessions` where `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.704148, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "SchoolSessionResource.php:59", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolSessionResource.php&line=59", "ajax": false, "filename": "SchoolSessionResource.php", "line": "59"}, "connection": "racoed", "explain": null, "start_percent": 65.225, "width_percent": 1.25}, {"sql": "select count(*) as aggregate from `semesters` where `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.708124, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "SemesterResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSemesterResource.php&line=49", "ajax": false, "filename": "SemesterResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 66.475, "width_percent": 0.815}, {"sql": "select count(*) as aggregate from `users` where `users`.`role` != 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7116442, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "StaffResource.php:41", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStaffResource.php&line=41", "ajax": false, "filename": "StaffResource.php", "line": "41"}, "connection": "racoed", "explain": null, "start_percent": 67.289, "width_percent": 0.885}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.715103, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "StudentResource.php:105", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=105", "ajax": false, "filename": "StudentResource.php", "line": "105"}, "connection": "racoed", "explain": null, "start_percent": 68.174, "width_percent": 0.969}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 8}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.720153, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 69.143, "width_percent": 0.73}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7228289, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 69.874, "width_percent": 0.576}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 order by `semester_start` asc", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, {"index": 16, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.725255, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:149", "source": {"index": 15, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=149", "ajax": false, "filename": "AcademicCalendarService.php", "line": "149"}, "connection": "racoed", "explain": null, "start_percent": 70.449, "width_percent": 0.576}, {"sql": "select * from `semesters` where `semesters`.`id` in (1, 2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, {"index": 21, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.727904, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:149", "source": {"index": 20, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 149}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=149", "ajax": false, "filename": "AcademicCalendarService.php", "line": "149"}, "connection": "racoed", "explain": null, "start_percent": 71.025, "width_percent": 0.52}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, {"index": 17, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 178}, {"index": 18, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.7305188, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:206", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=206", "ajax": false, "filename": "AcademicCalendarService.php", "line": "206"}, "connection": "racoed", "explain": null, "start_percent": 71.545, "width_percent": 0.562}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 183}, {"index": 17, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.733785, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:183", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=183", "ajax": false, "filename": "AcademicCalendarService.php", "line": "183"}, "connection": "racoed", "explain": null, "start_percent": 72.107, "width_percent": 1.334}, {"sql": "select * from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, {"index": 17, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.737986, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:189", "source": {"index": 16, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=189", "ajax": false, "filename": "AcademicCalendarService.php", "line": "189"}, "connection": "racoed", "explain": null, "start_percent": 73.441, "width_percent": 1.166}, {"sql": "select * from `semesters` where `semesters`.`id` in (1) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, {"index": 22, "namespace": "view", "name": "filament.hooks.school-details", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/school-details.blade.php", "line": 9}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.741926, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendarService.php:189", "source": {"index": 21, "namespace": null, "name": "app/Services/AcademicCalendarService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Services\\AcademicCalendarService.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FServices%2FAcademicCalendarService.php&line=189", "ajax": false, "filename": "AcademicCalendarService.php", "line": "189"}, "connection": "racoed", "explain": null, "start_percent": 74.607, "width_percent": 0.913}, {"sql": "select * from `cache` where `key` in ('settings_defaults')", "type": "query", "params": [], "bindings": ["settings_defaults"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.746989, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 75.52, "width_percent": 1.517}, {"sql": "select `name`, `payload` from `settings` where `group` = 'college'", "type": "query", "params": [], "bindings": ["college"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "app/Settings/MultiTenantSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Settings\\MultiTenantSettingsRepository.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 89}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 277}], "start": **********.751485, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:28", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=28", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 77.037, "width_percent": 2.907}, {"sql": "select * from `cache` where `key` in ('filament-easy-footer.github.mikailhamzat/racoed-cportal.latest-tag')", "type": "query", "params": [], "bindings": ["filament-easy-footer.github.mikailhamzat/racoed-cportal.latest-tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/devonab/filament-easy-footer/src/Services/GitHubService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\Services\\GitHubService.php", "line": 115}, {"index": 17, "namespace": null, "name": "vendor/devonab/filament-easy-footer/src/Services/GitHubService.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\devonab\\filament-easy-footer\\src\\Services\\GitHubService.php", "line": 76}], "start": **********.7855701, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 79.944, "width_percent": 0.885}, {"sql": "select count(*) as aggregate from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.792819, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "AssessmentResource.php:55", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/AssessmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\AssessmentResource.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FAssessmentResource.php&line=55", "ajax": false, "filename": "AssessmentResource.php", "line": "55"}, "connection": "racoed", "explain": null, "start_percent": 80.829, "width_percent": 0.716}, {"sql": "select count(*) as aggregate from `courses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7961361, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CourseResource.php:56", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/CourseResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\CourseResource.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FCourseResource.php&line=56", "ajax": false, "filename": "CourseResource.php", "line": "56"}, "connection": "racoed", "explain": null, "start_percent": 81.545, "width_percent": 0.716}, {"sql": "select count(*) as aggregate from `departments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7992709, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DepartmentResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/DepartmentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\DepartmentResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FDepartmentResource.php&line=51", "ajax": false, "filename": "DepartmentResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 82.261, "width_percent": 0.548}, {"sql": "select count(*) as aggregate from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.803267, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "GradeResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/GradeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\GradeResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FGradeResource.php&line=51", "ajax": false, "filename": "GradeResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 82.809, "width_percent": 2.037}, {"sql": "select count(*) as aggregate from `invoices` where `invoice_status` = 3 and ((`invoices`.`payable_type` = 'App\\\\Models\\\\Fee' and exists (select * from `fees` where `invoices`.`payable_id` = `fees`.`id`)) or (`invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and exists (select * from `registrations` where `invoices`.`payable_id` = `registrations`.`id`))) and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3, "App\\Models\\Fee", "App\\Models\\Registration"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.807244, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "InvoiceResource.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\InvoiceResource.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FInvoiceResource.php&line=53", "ajax": false, "filename": "InvoiceResource.php", "line": "53"}, "connection": "racoed", "explain": null, "start_percent": 84.846, "width_percent": 1.91}, {"sql": "select count(*) as aggregate from `levels` where `levels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.810716, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "LevelResource.php:50", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/LevelResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\LevelResource.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FLevelResource.php&line=50", "ajax": false, "filename": "LevelResource.php", "line": "50"}, "connection": "racoed", "explain": null, "start_percent": 86.756, "width_percent": 0.772}, {"sql": "select count(*) as aggregate from `news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.813788, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "NewsResource.php:62", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/NewsResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\NewsResource.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FNewsResource.php&line=62", "ajax": false, "filename": "NewsResource.php", "line": "62"}, "connection": "racoed", "explain": null, "start_percent": 87.528, "width_percent": 0.562}, {"sql": "select count(*) as aggregate from `programmes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.816896, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ProgrammeResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/ProgrammeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\ProgrammeResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FProgrammeResource.php&line=54", "ajax": false, "filename": "ProgrammeResource.php", "line": "54"}, "connection": "racoed", "explain": null, "start_percent": 88.09, "width_percent": 0.59}, {"sql": "select count(*) as aggregate from `schools`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.822074, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "SchoolResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolResource.php&line=49", "ajax": false, "filename": "SchoolResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 88.68, "width_percent": 1.517}, {"sql": "select count(*) as aggregate from `school_sessions` where `school_sessions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.825271, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SchoolSessionResource.php:59", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SchoolSessionResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SchoolSessionResource.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSchoolSessionResource.php&line=59", "ajax": false, "filename": "SchoolSessionResource.php", "line": "59"}, "connection": "racoed", "explain": null, "start_percent": 90.197, "width_percent": 0.744}, {"sql": "select count(*) as aggregate from `semesters` where `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.828376, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "SemesterResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/SemesterResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\SemesterResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FSemesterResource.php&line=49", "ajax": false, "filename": "SemesterResource.php", "line": "49"}, "connection": "racoed", "explain": null, "start_percent": 90.941, "width_percent": 0.646}, {"sql": "select count(*) as aggregate from `users` where `users`.`role` != 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.830946, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StaffResource.php:41", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StaffResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StaffResource.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStaffResource.php&line=41", "ajax": false, "filename": "StaffResource.php", "line": "41"}, "connection": "racoed", "explain": null, "start_percent": 91.587, "width_percent": 0.744}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.834485, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "StudentResource.php:105", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/StudentResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\StudentResource.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FStudentResource.php&line=105", "ajax": false, "filename": "StudentResource.php", "line": "105"}, "connection": "racoed", "explain": null, "start_percent": 92.331, "width_percent": 1.447}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiN0ZPcHhkUnAzalRhd2RMTlNmbnNnSzVhU0pNUzZ1emQ1MHhLalBYTiI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJDZKbGdVTW5wM3hnRElYY0JTVzl0Qk9ka2lNbDhjRkdwY1pwM21Sc0N4NE9zbWZEUVZZWU5HIjtzOjY6InRhYmxlcyI7YToxOntzOjIwOiJMaXN0U3R1ZGVudHNfZmlsdGVycyI7YToxOntzOjE0OiJzdHVkZW50X2ZpbHRlciI7YTo0OntzOjE3OiJzY2hvb2xfc2Vzc2lvbl9pZCI7czoxOiI0IjtzOjExOiJzZW1lc3Rlcl9pZCI7czoxOiIyIjtzOjg6ImxldmVsX2lkIjtzOjE6IjMiO3M6MTI6InByb2dyYW1tZV9pZCI7czoxOiIxIjt9fX1zOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyNTM6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L3N0YWZmL3N0dWRlbnRzP3RhYmxlRmlsdGVycyU1QnN0dWRlbnRfZmlsdGVyJTVEJTVCc2Nob29sX3Nlc3Npb25faWQlNUQ9NCZ0YWJsZUZpbHRlcnMlNUJzdHVkZW50X2ZpbHRlciU1RCU1QnNlbWVzdGVyX2lkJTVEPTImdGFibGVGaWx0ZXJzJTVCc3R1ZGVudF9maWx0ZXIlNUQlNUJsZXZlbF9pZCU1RD0zJnRhYmxlRmlsdGVycyU1QnN0dWRlbnRfZmlsdGVyJTVEJTVCcHJvZ3JhbW1lX2lkJTVEPTEiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'EEuMbIiZ26t6M7o3bQNpIUXpJDiaeiXoW0yIoOgx'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiN0ZPcHhkUnAzalRhd2RMTlNmbnNnSzVhU0pNUzZ1emQ1MHhLalBYTiI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJDZKbGdVTW5wM3hnRElYY0JTVzl0Qk9ka2lNbDhjRkdwY1pwM21Sc0N4NE9zbWZEUVZZWU5HIjtzOjY6InRhYmxlcyI7YToxOntzOjIwOiJMaXN0U3R1ZGVudHNfZmlsdGVycyI7YToxOntzOjE0OiJzdHVkZW50X2ZpbHRlciI7YTo0OntzOjE3OiJzY2hvb2xfc2Vzc2lvbl9pZCI7czoxOiI0IjtzOjExOiJzZW1lc3Rlcl9pZCI7czoxOiIyIjtzOjg6ImxldmVsX2lkIjtzOjE6IjMiO3M6MTI6InByb2dyYW1tZV9pZCI7czoxOiIxIjt9fX1zOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyNTM6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L3N0YWZmL3N0dWRlbnRzP3RhYmxlRmlsdGVycyU1QnN0dWRlbnRfZmlsdGVyJTVEJTVCc2Nob29sX3Nlc3Npb25faWQlNUQ9NCZ0YWJsZUZpbHRlcnMlNUJzdHVkZW50X2ZpbHRlciU1RCU1QnNlbWVzdGVyX2lkJTVEPTImdGFibGVGaWx0ZXJzJTVCc3R1ZGVudF9maWx0ZXIlNUQlNUJsZXZlbF9pZCU1RD0zJnRhYmxlRmlsdGVycyU1QnN0dWRlbnRfZmlsdGVyJTVEJTVCcHJvZ3JhbW1lX2lkJTVEPTEiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjg6ImZpbGFtZW50IjthOjA6e319", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "EEuMbIiZ26t6M7o3bQNpIUXpJDiaeiXoW0yIoOgx"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.9048629, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "racoed", "explain": null, "start_percent": 93.778, "width_percent": 6.222}]}, "models": {"data": {"Spatie\\LaravelSettings\\Models\\SettingsProperty": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FModels%2FSettingsProperty.php&line=1", "ajax": false, "filename": "SettingsProperty.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Semester": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\SemesterSchedule": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemesterSchedule.php&line=1", "ajax": false, "filename": "SemesterSchedule.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}, "App\\Models\\Programme": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FProgramme.php&line=1", "ajax": false, "filename": "Programme.php", "line": "?"}}}, "count": 28, "is_counter": true}, "livewire": {"data": {"app.filament.staff.resources.student-resource.pages.list-students #fUjXB0Xn6QvlRuLoDO3L": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"student_filter\" => array:4 [\n        \"school_session_id\" => \"4\"\n        \"semester_id\" => \"2\"\n        \"level_id\" => \"3\"\n        \"programme_id\" => \"1\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => \"registered\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"student_filter\" => array:4 [\n        \"school_session_id\" => \"4\"\n        \"semester_id\" => \"2\"\n        \"level_id\" => \"3\"\n        \"programme_id\" => \"1\"\n      ]\n    ]\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.staff.resources.student-resource.pages.list-students\"\n  \"component\" => \"App\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents\"\n  \"id\" => \"fUjXB0Xn6QvlRuLoDO3L\"\n]", "help-menu #sMadjkYWQycEjJYoiuMb": "array:4 [\n  \"data\" => array:11 [\n    \"documentation\" => []\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n  ]\n  \"name\" => \"help-menu\"\n  \"component\" => \"Guava\\FilamentKnowledgeBase\\Livewire\\HelpMenu\"\n  \"id\" => \"sMadjkYWQycEjJYoiuMb\"\n]", "devonab.filament-easy-footer.github-version #tzmSOAT8bDHqqRt0Ur8q": "array:4 [\n  \"data\" => array:4 [\n    \"showLogo\" => false\n    \"showUrl\" => false\n    \"version\" => \"v1.2.0\"\n    \"repository\" => \"mikailhamzat/racoed-cportal\"\n  ]\n  \"name\" => \"devonab.filament-easy-footer.github-version\"\n  \"component\" => \"Devonab\\FilamentEasyFooter\\Livewire\\GitHubVersion\"\n  \"id\" => \"tzmSOAT8bDHqqRt0Ur8q\"\n]", "filament.livewire.notifications #9BQ5Btgc2nGHGvMFQ9DB": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#4301\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"9BQ5Btgc2nGHGvMFQ9DB\"\n]", "modals #egV9FnKWK6DUCG1lpwrN": "array:4 [\n  \"data\" => array:11 [\n    \"documentable\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n  ]\n  \"name\" => \"modals\"\n  \"component\" => \"Guava\\FilamentKnowledgeBase\\Livewire\\Modals\"\n  \"id\" => \"egV9FnKWK6DUCG1lpwrN\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/staff/students?tableFilters%5Bstudent_filter%5D%5Bschool_session_id%5D=4&...", "action_name": "filament.staff.resources.students.index", "controller_action": "App\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents", "uri": "GET staff/students", "domain": "https://portal.racoed.test", "controller": "App\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "staff/students", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:staff, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "932ms", "peak_memory": "14MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>tableFilters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>student_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n      \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>programme_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1290298196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1290298196\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-210946272 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IldnUWtmS3dlcFkyam16cW9TZ3hIM3c9PSIsInZhbHVlIjoiQ2tZNmQ1RENEdHZGbkxqaTlDbjZ4YlR5SFZLdUJUbWRQOEFZR3ViSVoyMTBJNVJ1QWhnandiTGhYbEM3RVVPKzZhMlhDR1YvcnBTZEgvWW9wS2hoNWlVeEM1V3JLUUMzeWFSSDhQRUlNZWVIR3I5MlROTittbCtvNEN5VVdkTjAiLCJtYWMiOiJiZjJkMGQ0ZGUzMTllMGYyNDQ4NWMwYWY1ZGRiYmZmZTUxNGVhMjAzNzg3ZDE1YWY4YjljMWVmZDM0YzgwYzlkIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IkprUnZCNHo2YkMwKy9xbFhBb2VNY1E9PSIsInZhbHVlIjoiSlh6QnRuNGZVSzdvRHBpeHJzbG1DdlJsNndSQ2N5ZnlOeU43ODZPcWM5QVNQajZZL0ViZm9hTHk1QlYyS3NzaU9OOVZkNnRCMFI1bnk3akliaFUwNTRGVGVqQncwT21hUEt6cFJsaHF6bWxFK3habm5HVWd3TC9Fb0VRQkRSRk4iLCJtYWMiOiJjMWYzZmJhOTMwYzEyNDc0MDZhNDM2MWUzNzIxNTFjYjM4MDc3NWIyYWNlNjEyOTY0YTA2NTYzZGVmY2RmYjIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">https://portal.racoed.test/staff/students?tableFilters[student_filter][school_session_id]=4&amp;tableFilters[student_filter][semester_id]=2&amp;tableFilters[student_filter][level_id]=3&amp;tableFilters[student_filter][programme_id]=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210946272\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-9027206 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1H5vZ3kUjepNqOWpqLfZbHhO0p3Hpb1rxDVxB6uHj7v9NbxaMJIfpkgPgReu|$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7FOpxdRp3jTawdLNSfnsgK5aSJMS6uzd50xKjPXN</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EEuMbIiZ26t6M7o3bQNpIUXpJDiaeiXoW0yIoOgx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9027206\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-23201570 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 13 Nov 2025 17:32:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"457 characters\">XSRF-TOKEN=eyJpdiI6IjZhUE01RnBWMkdETk82VUhkS0Z1V2c9PSIsInZhbHVlIjoiQUFiditzS2ozdFNQRC9ucm5xTkEyZW5qeHh1R0RrOG5pK0o2ZUxRajFVZ1ZuVXUvSWRZaTY4ZWUwVjU1Y051L1NXYW0vSjVkZm5NU0RPVG1Hb2xJUHZxYktjeUxXWW1JcnZnZGJndUFwOFRXT093WDVTZzU2VXd3SHB5QlNIVzkiLCJtYWMiOiI1Y2MzYjM2YjliZWM4OWMwOWM1MDg0ODRhNDFiNzdmYzM1YjFiOTYyYmI1MmVkNjI5ZWVhM2VjOGVmYmY0NWFmIiwidGFnIjoiIn0%3D; expires=Thu, 13 Nov 2025 19:32:15 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"471 characters\">racoed_session=eyJpdiI6InNwOVVocHI1bktCdHpKZnZ3T2kyN3c9PSIsInZhbHVlIjoiZVJ3NlRwaElBb3VLVENYdzZYcEhCZ0tKSnVJU2p4VjRSckFtUDlZQjNjNmxaSWdZNFFxdnM4eDRTU01EQ1VSZ2crN3pCZWlubTMvaDdHUEswQ0txc09aWkxCYXQ0elhxMUMreGRYN2RJeC94WXU0Qmp2dEFaOTdYK0x0aDZsdFQiLCJtYWMiOiIyMzFhMDI2ZmM2ZDQzNzc2NzlhNmE1YjcyMTdlZDRhYjI0YjAzY2I5ZGVlNDkwYWM1Y2JiNWFhNjZkY2QwMmRiIiwidGFnIjoiIn0%3D; expires=Thu, 13 Nov 2025 19:32:15 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6IjZhUE01RnBWMkdETk82VUhkS0Z1V2c9PSIsInZhbHVlIjoiQUFiditzS2ozdFNQRC9ucm5xTkEyZW5qeHh1R0RrOG5pK0o2ZUxRajFVZ1ZuVXUvSWRZaTY4ZWUwVjU1Y051L1NXYW0vSjVkZm5NU0RPVG1Hb2xJUHZxYktjeUxXWW1JcnZnZGJndUFwOFRXT093WDVTZzU2VXd3SHB5QlNIVzkiLCJtYWMiOiI1Y2MzYjM2YjliZWM4OWMwOWM1MDg0ODRhNDFiNzdmYzM1YjFiOTYyYmI1MmVkNjI5ZWVhM2VjOGVmYmY0NWFmIiwidGFnIjoiIn0%3D; expires=Thu, 13-Nov-2025 19:32:15 GMT; domain=.racoed.test; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">racoed_session=eyJpdiI6InNwOVVocHI1bktCdHpKZnZ3T2kyN3c9PSIsInZhbHVlIjoiZVJ3NlRwaElBb3VLVENYdzZYcEhCZ0tKSnVJU2p4VjRSckFtUDlZQjNjNmxaSWdZNFFxdnM4eDRTU01EQ1VSZ2crN3pCZWlubTMvaDdHUEswQ0txc09aWkxCYXQ0elhxMUMreGRYN2RJeC94WXU0Qmp2dEFaOTdYK0x0aDZsdFQiLCJtYWMiOiIyMzFhMDI2ZmM2ZDQzNzc2NzlhNmE1YjcyMTdlZDRhYjI0YjAzY2I5ZGVlNDkwYWM1Y2JiNWFhNjZkY2QwMmRiIiwidGFnIjoiIn0%3D; expires=Thu, 13-Nov-2025 19:32:15 GMT; domain=.racoed.test; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23201570\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-777160333 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7FOpxdRp3jTawdLNSfnsgK5aSJMS6uzd50xKjPXN</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ListStudents_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>student_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>programme_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"253 characters\">https://portal.racoed.test/staff/students?tableFilters%5Bstudent_filter%5D%5Bschool_session_id%5D=4&amp;tableFilters%5Bstudent_filter%5D%5Bsemester_id%5D=2&amp;tableFilters%5Bstudent_filter%5D%5Blevel_id%5D=3&amp;tableFilters%5Bstudent_filter%5D%5Bprogramme_id%5D=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777160333\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/staff/students?tableFilters%5Bstudent_filter%5D%5Bschool_session_id%5D=4&...", "action_name": "filament.staff.resources.students.index", "controller_action": "App\\Filament\\Staff\\Resources\\StudentResource\\Pages\\ListStudents"}, "badge": null}}