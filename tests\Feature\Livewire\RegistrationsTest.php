<?php

use App\Enums\AddressState;
use App\Enums\Role;
use App\Enums\Title;
use App\Livewire\Registrations;
use App\Models\Level;
use App\Models\Programme;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Semester;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Livewire\Livewire;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create necessary related records
    $this->schoolSession = SchoolSession::create(['name' => '2024/2025', 'is_active' => true]);
    $this->semester = Semester::create(['name' => 'First semester']);
    $this->level = Level::create(['name' => 'NCE 1']);

    // Create school and department for programme
    $school = \App\Models\School::create(['name' => 'Sciences']);
    $department = \App\Models\Department::create([
        'name' => 'Computer Science',
        'code' => 'CSC',
        'school_id' => $school->id,
    ]);

    $this->programme = Programme::create([
        'name' => 'Computer Science',
        'code' => 'CS',
        'first_department_id' => $department->id,
        'school_id' => $school->id,
    ]);

    // Create a staff user (non-student)
    $this->staff = User::create([
        'title' => Title::MR,
        'last_name' => 'Staff',
        'first_name' => 'Test',
        'middle_name' => 'User',
        'phone' => '08100000000',
        'email' => '<EMAIL>',
        'role' => Role::ICT,
        'address_line' => 'Test Address',
        'address_town' => 'Test Town',
        'address_state' => AddressState::LAGOS,
        'password' => Hash::make('password'),
    ]);

    // Create a student user
    $this->student = User::create([
        'title' => Title::MR,
        'last_name' => 'Student',
        'first_name' => 'Test',
        'middle_name' => 'User',
        'phone' => '08100000001',
        'email' => '<EMAIL>',
        'role' => Role::STUDENT,
        'address_line' => 'Student Address',
        'address_town' => 'Student Town',
        'address_state' => AddressState::LAGOS,
        'password' => Hash::make('password'),
    ]);

    // Create a registration for the student
    $this->registration = Registration::create([
        'user_id' => $this->student->id,
        'school_session_id' => $this->schoolSession->id,
        'semester_id' => $this->semester->id,
        'level_id' => $this->level->id,
        'programme_id' => $this->programme->id,
        'is_active' => true,
    ]);
});

test('staff can delete a registration', function () {
    actingAs($this->staff);

    expect(Registration::count())->toBe(1);

    Livewire::test(Registrations::class, ['student' => $this->student])
        ->mountAction('deleteRegistration', ['registrationId' => $this->registration->id])
        ->callMountedAction();

    expect(Registration::count())->toBe(0);
});

test('student cannot delete a registration', function () {
    actingAs($this->student);

    $component = Livewire::test(Registrations::class, ['student' => $this->student]);

    expect($component->isDisabled)->toBeTrue();

    assertDatabaseHas('registrations', [
        'id' => $this->registration->id,
    ]);
});

test('delete action shows confirmation modal', function () {
    actingAs($this->staff);

    Livewire::test(Registrations::class, ['student' => $this->student])
        ->mountAction('deleteRegistration', ['registrationId' => $this->registration->id])
        ->assertActionHalted('deleteRegistration');
});

test('delete action with invalid registration id shows error', function () {
    actingAs($this->staff);

    Livewire::test(Registrations::class, ['student' => $this->student])
        ->callAction('deleteRegistration', ['registrationId' => 99999])
        ->assertNotified('Failed to delete registration.');
});

test('registrations component renders all student registrations', function () {
    actingAs($this->staff);

    // Create another registration with different semester to avoid unique constraint
    $secondSemester = Semester::create(['name' => 'Second semester']);
    $secondRegistration = Registration::create([
        'user_id' => $this->student->id,
        'school_session_id' => $this->schoolSession->id,
        'semester_id' => $secondSemester->id,
        'level_id' => $this->level->id,
        'programme_id' => $this->programme->id,
        'is_active' => false,
    ]);

    Livewire::test(Registrations::class, ['student' => $this->student])
        ->assertSee($this->schoolSession->name)
        ->assertSee($this->semester->name)
        ->assertSee($this->level->name)
        ->assertSee($this->programme->name)
        ->assertViewHas('registrations', function ($registrations) {
            return $registrations->count() === 2;
        });
});
