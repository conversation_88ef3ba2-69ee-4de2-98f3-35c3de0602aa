<?php

namespace App\Livewire;

use App\Enums\Role;
use App\Models\Registration;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class Registrations extends Component implements HasActions, HasForms
{
    use InteractsWithActions;
    use InteractsWithForms;

    public User $student;

    public bool $isDisabled = false;

    public function mount(User $student)
    {
        $this->student = $student;
        $this->isDisabled = Auth::user()->role === Role::STUDENT;
    }

    public function toggle($registrationId): void
    {
        if ($this->isDisabled) {
            return;
        }

        $registration = Registration::find($registrationId);
        $newStatus = ! $registration->is_active;

        if ($newStatus) {
            $this->student->registrations()->update(['is_active' => false]);
        }

        $registration->update(['is_active' => $newStatus]);
    }

    public function printRegistration($id)
    { /* your logic */
    }

    public function downloadRegistration($id)
    { /* your logic */
    }

    public function viewRegistration($id)
    { /* your logic */
    }

    public function deleteRegistration($registrationId)
    {
        $this->mountAction('deleteRegistrationAction', ['registrationId' => $registrationId]);
    }

    public function deleteRegistrationDirect($registrationId)
    {
        try {
            $registration = Registration::findOrFail($registrationId);
            $registration->delete();

            Notification::make()
                ->title('Registration deleted')
                ->body('The registration has been successfully deleted.')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Log::error('Error deleting registration: ' . $e->getMessage());

            Notification::make()
                ->title('Failed to delete registration.')
                ->body('An error occurred while trying to delete the registration.')
                ->danger()
                ->send();
        }
    }

    public function deleteRegistrationAction(): Action
    {
        return Action::make('deleteRegistrationAction')
            ->color('danger')
            ->icon('heroicon-s-trash')
            ->requiresConfirmation()
            ->modalHeading('Delete Registration')
            ->modalDescription('Are you sure you want to delete this registration? This action cannot be undone.')
            ->modalSubmitActionLabel('Delete')
            ->modalCancelActionLabel('Cancel')
            ->action(function (array $arguments) {
                try {
                    $registrationId = $arguments['registrationId'];
                    $registration = Registration::findOrFail($registrationId);
                    $registration->delete();

                    Notification::make()
                        ->title('Registration deleted')
                        ->body('The registration has been successfully deleted.')
                        ->success()
                        ->send();
                } catch (\Exception $e) {
                    Log::error('Error deleting registration: ' . $e->getMessage());

                    Notification::make()
                        ->title('Failed to delete registration.')
                        ->body('An error occurred while trying to delete the registration.')
                        ->danger()
                        ->send();
                }
            });
    }

    public function render()
    {
        $registrations = $this->student->registrations()
            ->with(['schoolSession', 'semester', 'level', 'programme'])
            ->orderBy('created_at')
            ->get();

        return view('livewire.registrations', compact('registrations'));
    }
}
