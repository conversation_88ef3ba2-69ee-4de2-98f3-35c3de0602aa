<?php

namespace App\Filament\Staff\Resources\StudentResource\Pages;

use App\Enums\Role;
use App\Models\User;
use App\Models\Level;
use Filament\Actions;
use App\Models\Semester;
use App\Models\Programme;
use Illuminate\Support\Str;
use App\Models\Registration;
use Filament\Actions\Action;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\File;
use Filament\Resources\Components\Tab;
use Filament\Notifications\Notification;
use App\Filament\Imports\StudentImporter;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Staff\Resources\StudentResource;

class ListStudents extends ListRecords
{
    protected static string $resource = StudentResource::class;



    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->visible(fn() => Auth::user()->role === Role::ICT)
                ->label('Import Students')
                ->importer(StudentImporter::class)
                ->maxRows(100)
                ->fileRules([
                    File::types(['csv', 'txt'])->max(1024),
                ]),
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $filters = self::extractFilters($this);
        $filteredSessionId = $filters['school_session_id'] ?? null;
        $filteredSemesterId = $filters['semester_id'] ?? null;

        $registeredQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereDoesntHave('registrations', fn($q) => $q->where('is_graduated', true))
            ->whereDoesntHave('registrations', fn($q) => $q->where('is_withdrawn', true));

        $pendingQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::PENDING));

        $deniedQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::DENIED));

        $graduatedQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereHas('registrations', fn($q) => $q->where('is_graduated', true));

        $withdrawnQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereHas('registrations', fn($q) => $q->where('is_withdrawn', true));

        $getBadgeCount = function (callable $queryModifier) use ($filteredSessionId, $filteredSemesterId): int {
            $query = $this->getResource()::getEloquentQuery();

            $queryModifier($query);

            if ($filteredSessionId && $filteredSemesterId) {
                $query->whereHas('registrations', fn($q) => $q->where('school_session_id', $filteredSessionId)->where('semester_id', $filteredSemesterId));
            }

            return $query->count();
        };

        return [
            'registered' => Tab::make('registered')
                ->label('Registered')
                ->modifyQueryUsing($registeredQuery)
                ->badge($getBadgeCount($registeredQuery)),

            'pending' => Tab::make('pending')
                ->label('Pending')
                ->modifyQueryUsing($pendingQuery)
                ->badge($getBadgeCount($pendingQuery)),

            'denied' => Tab::make('denied')
                ->label('Denied')
                ->modifyQueryUsing($deniedQuery)
                ->badge($getBadgeCount($deniedQuery)),

            'graduated' => Tab::make('graduated')
                ->label('Graduated')
                ->modifyQueryUsing($graduatedQuery)
                ->badge($getBadgeCount($graduatedQuery)),

            'withdrawn' => Tab::make('withdrawn')
                ->label('Withdrawn')
                ->modifyQueryUsing($withdrawnQuery)
                ->badge($getBadgeCount($withdrawnQuery)),
        ];
    }

    private static function extractFilters($livewire): array
    {
        $filters = $livewire->tableFilters['student_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'programme_id' => $filters['programme_id'] ?? null,
        ];
    }

    public function printRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.print', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'Registration',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Registration print failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Print Error')
                ->body('Unable to print registration. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function downloadRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.download', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Registration download failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Download Error')
                ->body('Unable to download registration. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function viewRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.view', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("window.open('$url', '_blank');");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Registration view failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('View Error')
                ->body('Unable to view registration. Please try again later.')
                ->danger()
                ->send();
        }
    }


    public function confirmRegistrationsAction(): Action
    {
        return Action::make('confirmRegistrations')
            ->requiresConfirmation()
            ->modalHeading('Confirm bulk registration?')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->modalDescription(function (array $arguments) {
                $tableRecords = $arguments['tableRecords'];
                $registrationData = $arguments['registrationData'];

                $numberOfStudents = count($tableRecords);

                $newSession = SchoolSession::find($registrationData['school_session_id'])?->name ?? '';
                $newSemester = Semester::find($registrationData['semester_id'])?->name ?? '';
                $newLevel = Level::find($registrationData['level_id'])?->name ?? '';
                $newProgramme = Programme::find($registrationData['programme_id'])?->name ?? '';

                return new HtmlString("
                    Are you sure you want to register <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b> to:
                    <br><b>{$newSession}</b> session,
                    <b>{$newSemester}</b>,
                    <b>{$newLevel}</b> level,
                    <b>{$newProgramme}</b> programme?
                    <br><br><i>Note: This will create new registrations or update existing ones for the same session and semester.</i>
                    <br><i>This action cannot be undone.</i>
                ");
            })
            ->action(function ($arguments) {
                $tableRecords = $arguments['tableRecords'];
                $registrationData = $arguments['registrationData'];

                $studentCounts = DB::transaction(function () use ($tableRecords, $registrationData) {
                    $updated = 0;
                    $created = 0;
                    $skipped = 0;

                    foreach ($tableRecords as $studentData) {
                        try {
                            $record = User::find($studentData['id']);
                            if (! $record) {
                                continue;
                            }

                            $alreadyRegistered = $record->registrations()
                                ->where('school_session_id', $registrationData['school_session_id'])
                                ->where('semester_id', $registrationData['semester_id'])
                                ->where('level_id', $registrationData['level_id'])
                                ->where('programme_id', $registrationData['programme_id'])
                                ->exists();

                            if ($alreadyRegistered) {
                                $skipped++;

                                continue;
                            }

                            $sameSessionAndSemester = $record->registrations()
                                ->where('school_session_id', $registrationData['school_session_id'])
                                ->where('semester_id', $registrationData['semester_id'])
                                ->first();

                            if (($registrationData['is_active'] ?? false) === true) {
                                $record->registrations()
                                    ->when($sameSessionAndSemester, fn($q) => $q->where('id', '!=', $sameSessionAndSemester->id))
                                    ->update(['is_active' => false]);
                            }

                            if ($sameSessionAndSemester) {
                                $sameSessionAndSemester->update([
                                    'semester_id' => $registrationData['semester_id'],
                                    'level_id' => $registrationData['level_id'],
                                    'programme_id' => $registrationData['programme_id'],
                                    'is_active' => $registrationData['is_active'] ?? false,
                                ]);
                                $updated++;
                            } else {
                                $record->registrations()->create($registrationData);
                                $created++;
                            }
                        } catch (\Exception $e) {
                            Log::error('Registration confirmation failed:', ['error' => $e->getMessage()]);
                            $skipped++;
                        }
                    }

                    return ['updated' => $updated, 'created' => $created, 'skipped' => $skipped];
                });

                $newSession = SchoolSession::find($registrationData['school_session_id'])?->name ?? '';
                $newSemester = Semester::find($registrationData['semester_id'])?->name ?? '';
                $newLevel = Level::find($registrationData['level_id'])?->name ?? '';
                $newProgramme = Programme::find($registrationData['programme_id'])?->name ?? '';

                if ($studentCounts['created'] > 0) {
                    Notification::make()
                        ->success()
                        ->title('Students Registered')
                        ->body("<b>{$studentCounts['created']} " . Str::plural('student', $studentCounts['created']) . "</b> registered to <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                        ->send();
                }

                if ($studentCounts['updated'] > 0) {
                    Notification::make()
                        ->success()
                        ->title('Registrations Updated')
                        ->body("<b>{$studentCounts['updated']} " . Str::plural('student', $studentCounts['updated']) . "</b> registration updated for <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                        ->send();
                }

                if ($studentCounts['skipped'] > 0 && $studentCounts['created'] === 0 && $studentCounts['updated'] === 0) {
                    Notification::make()
                        ->warning()
                        ->title('No Students Processed')
                        ->body("<b>{$studentCounts['skipped']} " . Str::plural('student', $studentCounts['skipped']) . '</b> skipped due to existing registrations or errors.')
                        ->send();
                }
            });
    }

    public function confirmGraduationsAction(): Action
    {
        return Action::make('confirmGraduations')
            ->requiresConfirmation()
            ->modalHeading('Confirm bulk graduation?')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->modalDescription(function (array $arguments) {
                $tableRecords = $arguments['tableRecords'];
                $sessionName = $arguments['sessionName'];

                $numberOfStudents = count($tableRecords);

                return new HtmlString("
                    Are you sure you'd like to graduate <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b> from <b>{$sessionName}</b> session?
                    <br><br>The students will not have active registration but their records will remain in the portal and they will have access to them.
                    <br><br><i>This action cannot be undone.</i>
                ");
            })
            ->action(function ($arguments) {
                $tableRecords = $arguments['tableRecords'];
                $filteredSessionId = $arguments['filteredSessionId'];
                $filteredSemesterId = $arguments['filteredSemesterId'];
                $sessionName = $arguments['sessionName'];

                $results = DB::transaction(function () use ($tableRecords, $filteredSessionId, $filteredSemesterId) {
                    $successCount = 0;
                    $skippedStudents = [];

                    foreach ($tableRecords as $studentData) {
                        try {
                            $record = User::find($studentData['id']);
                            if (! $record) {
                                continue;
                            }

                            // Validation 1: Minimum semesters (at least 6)
                            if ($record->registrations()->count() < 6) {
                                $skippedStudents[] = [
                                    'name' => $record->name,
                                    'reason' => 'Minimum 6 semesters required',
                                    'type' => 'semesters',
                                ];

                                continue;
                            }

                            // Validation 2: Outstanding courses
                            $outstandingCourses = StudentResource::getCumulativeOutstandingCourses($record);
                            if ($outstandingCourses->isNotEmpty()) {
                                $skippedStudents[] = [
                                    'name' => $record->name,
                                    'reason' => $outstandingCourses->map(fn($course) => $course->code)->join(', '),
                                    'type' => 'courses',
                                ];

                                continue;
                            }

                            // Validation 3: Already graduated
                            if ($record->registrations()->where('is_graduated', true)->exists()) {
                                $skippedStudents[] = [
                                    'name' => $record->name,
                                    'reason' => 'Already graduated',
                                    'type' => 'graduated',
                                ];

                                continue;
                            }

                            // Mark graduation
                            if ($filteredSessionId && $filteredSemesterId) {
                                $record->registrations()
                                    ->where('school_session_id', $filteredSessionId)
                                    ->where('semester_id', $filteredSemesterId)
                                    ->update(['is_graduated' => true]);
                            }

                            // Deactivate active registrations only (like single method)
                            $record->registrations()
                                ->where('is_active', true)
                                ->update(['is_active' => false]);

                            // Send database notification to student (if no unpaid portal fee)
                            if (! $record->hasUnpaidPortalFee()) {
                                Notification::make()
                                    ->title('Graduation Successful')
                                    ->icon('heroicon-o-academic-cap')
                                    ->iconColor('success')
                                    ->body('You have successfully graduated from the college. Congratulations!')
                                    ->sendToDatabase($record);
                            }

                            $successCount++;
                        } catch (\Exception $e) {
                            Log::error('Bulk graduation failed for student:', [
                                'studentId' => $studentData['id'],
                                'error' => $e->getMessage(),
                            ]);

                            $skippedStudents[] = [
                                'name' => $record->name ?? 'Unknown',
                                'reason' => 'Processing error',
                                'type' => 'error',
                            ];
                        }
                    }

                    return ['successCount' => $successCount, 'skippedStudents' => $skippedStudents];
                });

                // Show success notification
                if ($results['successCount'] > 0) {
                    Notification::make()
                        ->success()
                        ->title('Students Graduated')
                        ->body("<b>{$results['successCount']} " . Str::plural('student', $results['successCount']) . "</b> graduated from <b>{$sessionName}</b> session successfully.")
                        ->send();
                }

                // Show detailed warning notifications for skipped students
                if (! empty($results['skippedStudents'])) {
                    $groupedSkipped = collect($results['skippedStudents'])->groupBy('type');

                    // Outstanding courses notification
                    if ($groupedSkipped->has('courses')) {
                        $coursesList = $groupedSkipped['courses']->map(
                            fn($student) => "<b>{$student['name']}</b> (Outstanding: {$student['reason']})"
                        )->join('<br>');

                        Notification::make()
                            ->warning()
                            ->title('Students Skipped - Outstanding Courses')
                            ->body(new HtmlString("The following students were skipped due to outstanding courses:<br><br>{$coursesList}"))
                            ->persistent()
                            ->send();
                    }

                    // Minimum semesters notification
                    if ($groupedSkipped->has('semesters')) {
                        $semestersList = $groupedSkipped['semesters']->map(
                            fn($student) => "<b>{$student['name']}</b>"
                        )->join('<br>');

                        Notification::make()
                            ->warning()
                            ->title('Students Skipped - Insufficient Semesters')
                            ->body(new HtmlString("The following students were skipped (minimum 6 semesters required):<br><br>{$semestersList}"))
                            ->persistent()
                            ->send();
                    }

                    // Already graduated notification
                    if ($groupedSkipped->has('graduated')) {
                        $graduatedList = $groupedSkipped['graduated']->map(
                            fn($student) => "<b>{$student['name']}</b>"
                        )->join('<br>');

                        Notification::make()
                            ->warning()
                            ->title('Students Skipped - Already Graduated')
                            ->body(new HtmlString("The following students were skipped (already graduated):<br><br>{$graduatedList}"))
                            ->persistent()
                            ->send();
                    }

                    // Processing errors notification
                    if ($groupedSkipped->has('error')) {
                        $errorList = $groupedSkipped['error']->map(
                            fn($student) => "<b>{$student['name']}</b>"
                        )->join('<br>');

                        Notification::make()
                            ->danger()
                            ->title('Students Skipped - Processing Errors')
                            ->body(new HtmlString("The following students were skipped due to processing errors:<br><br>{$errorList}"))
                            ->persistent()
                            ->send();
                    }
                }
            });
    }

    public function confirmWithdrawalsAction(): Action
    {
        return Action::make('confirmWithdrawals')
            ->requiresConfirmation()
            ->modalHeading('Confirm bulk withdrawal?')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->modalDescription(function (array $arguments) {
                $tableRecords = $arguments['tableRecords'];
                $sessionName = $arguments['sessionName'];

                $numberOfStudents = count($tableRecords);

                return new HtmlString("
                    Are you sure you'd like to withdraw <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b> from <b>{$sessionName}</b> session?
                    <br><br>The students will not have active registration but their records will remain in the portal and they will have access to them.
                    <br><br><i>This action cannot be undone.</i>
                ");
            })
            ->action(function ($arguments) {
                $tableRecords = $arguments['tableRecords'];
                $filteredSessionId = $arguments['filteredSessionId'];
                $sessionName = $arguments['sessionName'];

                $successCount = 0;
                foreach ($tableRecords as $studentData) {
                    $record = User::find($studentData['id']);
                    if ($record) {
                        $record->registrations()
                            ->where('school_session_id', $filteredSessionId)
                            ->update([
                                'is_withdrawn' => true,
                                'is_active' => false,
                            ]);
                        $successCount++;
                    }
                }

                Notification::make()
                    ->success()
                    ->title('Students Withdrawn')
                    ->body("<b>{$successCount} " . Str::plural('student', $successCount) . "</b> withdrawn from <b>{$sessionName}</b> session successfully.")
                    ->send();
            });
    }

    public function getDefaultActiveTab(): string|int|null
    {
        return 'registered';
    }
}
