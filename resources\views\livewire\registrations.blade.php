<div>
    <table class="table-auto border w-full text-left my-4">
        <thead>
            <tr class="bg-gray-100">
                <th class="border px-4 py-2 text-center" colspan="7">Course Registrations</th>
            </tr>
            <tr class="bg-gray-100 text-sm">
                <th class="border px-4 py-2">Session</th>
                <th class="border px-4 py-2">Semester</th>
                <th class="border px-4 py-2">Level</th>
                <th class="border px-4 py-2">Programme</th>
                <th class="border px-4 py-2">Status</th>
                <th class="border px-4 py-2"></th>
                <th class="border px-4 py-2"></th>
            </tr>
        </thead>
        <tbody>
            @foreach ($registrations as $registration)
            <tr class="text-sm">
                <td class="border px-4 py-2">{{ $registration->schoolSession->name }}</td>
                <td class="border px-4 py-2">{{ $registration->semester->name }}</td>
                <td class="border px-4 py-2">{{ $registration->level->name }}</td>
                <td class="border px-4 py-2">{{ $registration->programme->name }}</td>
                <td class="border px-4 py-2">
                    <button
                        class="relative inline-flex h-6 w-11 shrink-0 rounded-full border-2 border-transparent outline-none transition-colors duration-200 ease-in-out {{ $registration->is_active ? 'bg-red-800' : 'bg-gray-200' }} {{ $isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer' }}"
                        wire:click="toggle({{ $registration->id }})" @click.stop.prevent @if($isDisabled) disabled
                        @endif>
                        <span
                            class="pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {{ $registration->is_active ? 'translate-x-5' : 'translate-x-0' }}"></span>
                    </button>
                </td>
                <td class="border px-4 py-2">
                    <x-filament::dropdown placement="bottom-start">
                        <x-slot name="trigger">
                            <x-filament::icon-button icon="heroicon-m-document-text" color="primary"
                                x-tooltip.raw="Export registration" />
                        </x-slot>
                        <x-filament::dropdown.list>

                            {{-- NOTE: Pause on this limitation for now till second semester 2025 --}}
                            {{-- @if ($student->role === Role::STUDENT && $registration->portalInvoice?->invoice_status
                            !== InvoiceStatus::PAID)
                            <p class="text-red-500 pl-2">No portal fee</p>
                            @else --}}
                            <x-filament::dropdown.list.item icon="heroicon-s-printer"
                                wire:click="printRegistration({{ $registration->id }})">
                                Print
                            </x-filament::dropdown.list.item>
                            <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down"
                                wire:click="downloadRegistration({{ $registration->id }})">
                                Download
                            </x-filament::dropdown.list.item>
                            {{-- @endif --}}
                        </x-filament::dropdown.list>
                    </x-filament::dropdown>
                </td>
                <td class="border px-4 py-2">
                    <x-filament::dropdown placement="bottom-start">
                        <x-slot name="trigger">
                            <x-filament::icon-button icon="heroicon-m-ellipsis-vertical" color="primary"
                                x-tooltip.raw="Registration actions" />
                        </x-slot>
                        <x-filament::dropdown.list>

                            {{-- NOTE: Pause on this limitation for now till second semester 2025 --}}
                            {{-- @if ($student->role === Role::STUDENT && $registration->portalInvoice?->invoice_status
                            !== InvoiceStatus::PAID)
                            <p class="text-red-500 pl-2">No portal fee</p>
                            @else --}}
                            <x-filament::dropdown.list.item icon="heroicon-s-eye"
                                wire:click="viewRegistration({{ $registration->id }})">
                                View
                            </x-filament::dropdown.list.item>
                            <x-filament::dropdown.list.item icon="heroicon-s-trash" color="danger"
                                wire:click.prevent="mountAction('deleteRegistration', { registrationId: {{ $registration->id }} })"
                                @click.stop>
                                Delete
                            </x-filament::dropdown.list.item>
                            {{-- @endif --}}
                        </x-filament::dropdown.list>
                    </x-filament::dropdown>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <x-filament-actions::modals />
</div>